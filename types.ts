
export enum SkillLevelKey {
  A1 = 'A1',
  A2 = 'A2',
  B1 = 'B1',
  B2 = 'B2',
  C1 = 'C1',
  C2 = 'C2',
  NATIVE = 'NATIVE', // Kept for simplicity, though Europass might just list it under mother tongue
}

export interface IdItem {
  id: string;
}

export interface LanguageSkill extends IdItem {
  language: string;
  // Europass levels: Understanding (Listening, Reading), Speaking (Spoken Interaction, Spoken Production), Writing
  listening: SkillLevelKey | '';
  reading: Skill<PERSON>evel<PERSON>ey | '';
  spokenInteraction: SkillLevelKey | '';
  spokenProduction: SkillLevelKey | '';
  writing: SkillLevel<PERSON>ey | '';
}

export interface RecurringPattern {
  startYear: number; // Anno di inizio (es. 2000)
  endYear: number; // Anno di fine (es. 2010)
  startMonth: number; // Mese di inizio (1-12, es. 6 per giugno)
  endMonth: number; // Mese di fine (1-12, es. 9 per settembre)
  description?: string; // Descrizione opzionale del pattern (es. "Lavoro estivo")
}

export interface WorkExperienceItem extends IdItem {
  startDate: string; // MM/YYYY
  endDate: string; // MM/YYYY
  current: boolean;
  occupationOrPositionHeld: string;
  employer: string;
  employerAddress?: string; // Optional but good for detail
  city?: string; // Often part of employerAddress or separate
  country?: string; // Often part of employerAddress or separate
  typeOfBusinessOrSector?: string;
  mainActivitiesAndResponsibilities: string;

  // Nuovi campi per esperienze ricorrenti
  isRecurring?: boolean; // Se true, questa è un'esperienza ricorrente
  recurringPattern?: RecurringPattern; // Pattern di ricorrenza
  displayAsGroup?: boolean; // Se true, mostra come gruppo compatto nel PDF
}

export interface EducationTrainingItem extends IdItem {
  startDate: string; // MM/YYYY
  endDate: string; // MM/YYYY
  current: boolean;
  titleOfQualificationAwarded: string;
  organisationNameAndAddress?: string; // Can be combined or split
  organisationName: string; // Simplified
  organisationAddress?: string; // Optional
  mainSubjectsOrSkillsCovered: string; // Europass often lists these as bullet points or a paragraph
  levelInNationalOrInternationalClassification?: string; // e.g., EQF
}

export interface PersonalSkills {
  motherTongue: string; // Singular as per Europass
  otherLanguages: LanguageSkill[];
  communicationSkills?: string;
  organisationalOrManagerialSkills?: string;
  professionalSkills?: string; // Often 'Job-related skills' or 'Technical skills'
  digitalSkills?: string;
  artisticSkills?: string;
  otherSkills?: string;
}

export interface InstantMessaging extends IdItem {
  service: string; // e.g., Skype, WhatsApp
  username: string;
}

export interface PersonalInformation {
  name: string; // Changed from firstName
  surname: string; // Changed from lastName
  photo?: string; // Base64 encoded image string
  address?: string;
  postalCode?: string;
  city?: string;
  country?: string;
  telephones?: string[]; // Europass uses 'Telephone(s)'
  emailAddresses?: string[]; // Europass uses 'Email(s)'
  websites?: string[]; // Europass often lists one primary website/portfolio
  linkedinProfile?: string; // Specific field for LinkedIn
  instantMessaging?: InstantMessaging[];
  sex?: string; // e.g., Male, Female, Other - Europass uses 'Sex'
  dateOfBirth?: string; // DD/MM/YYYY
  nationality?: string;
}

export interface Attachment extends IdItem {
    name: string;
    description?: string;
}

export interface CVData {
  id: string;
  title: string; // Name for this CV document (e.g., "CV John Doe - Marketing")
  lastUpdate: string;

  personalInformation: PersonalInformation;
  
  // Europass section: "TYPE OF APPLICATION" or "JOB APPLIED FOR / PREFERRED JOB / STUDIES APPLIED FOR / PERSONAL STATEMENT"
  desiredEmploymentOrPersonalStatement?: string; // Combines desired occupation and sector, or allows a personal statement
  
  workExperience: WorkExperienceItem[];
  educationTraining: EducationTrainingItem[];
  personalSkills: PersonalSkills;
  
  drivingLicence?: string; // Europass usually has 'Driving licence(s)' and then categories, e.g. "B"
  additionalInformation?: string; // For Hobbies, References, Courses, Publications etc.
  attachments?: Attachment[]; // List of attached documents
}


// Props for RepeatableFieldSection, unchanged but used by updated items
export interface RepeatableSectionProps<T extends IdItem> {
  items: T[];
  setItems: React.Dispatch<React.SetStateAction<T[]>>;
  renderItem: (item: T, index: number, onChange: (updatedItem: T) => void, onRemove: (id: string) => void) => React.ReactNode;
  createEmptyItem: () => T;
  title: string;
  addItemText?: string;
}

export type LanguageCode = 'en' | 'it';

export interface Translations {
  [key: string]: string | Translations;
}

export interface LanguageContextType {
  language: LanguageCode;
  setLanguage: (language: LanguageCode) => void;
  t: (key: string, replacements?: Record<string, string | number>) => string;
  getLocalizedDate: (dateString: string, options?: Intl.DateTimeFormatOptions) => string;
}
