
import React from 'react';
import { PersonalSkills, LanguageSkill, SkillLevelKey } from '../../../types';
import Input from '../../common/Input';
import Textarea from '../../common/Textarea';
import Select from '../../common/Select';
import RepeatableFieldSection, { ItemActions } from '../../common/RepeatableFieldSection';
// import Button from '../../common/Button'; // Not used for mother tongue add/remove anymore
import { v4 as uuidv4 } from 'uuid';
// import { PlusIcon, TrashIcon } from 'lucide-react'; // Not used for mother tongue
import type { ChangeEvent } from 'react';
import { useLanguage } from '../../../contexts/LanguageContext';

interface PersonalSkillsSectionProps {
  data: PersonalSkills;
  onChange: <K extends keyof PersonalSkills>(field: K, value: PersonalSkills[K]) => void;
}

const createEmptyLanguageSkill = (): LanguageSkill => ({
  id: uuidv4(),
  language: '',
  listening: '',
  reading: '',
  spokenInteraction: '',
  spokenProduction: '',
  writing: '',
});

const LanguageSkillItem: React.FC<{
  item: LanguageSkill;
  onChange: (updatedItem: LanguageSkill) => void;
  onRemove: (id: string) => void;
}> = ({ item, onChange, onRemove }) => {
  const { t } = useLanguage();
  const handleChange = (field: keyof LanguageSkill, value: any) => {
    onChange({ ...item, [field]: value });
  };
  
  const skillLevelOptions = Object.values(SkillLevelKey).map(key => ({ 
    value: key, 
    label: t(`skillLevels.${key}`) 
  }));

  const languageSkillSubLabels: Record<Exclude<keyof LanguageSkill, 'id' | 'language'>, string> = {
    listening: t('personalSkillsSection.listening'),
    reading: t('personalSkillsSection.reading'),
    spokenInteraction: t('personalSkillsSection.spokenInteraction'),
    spokenProduction: t('personalSkillsSection.spokenProduction'),
    writing: t('personalSkillsSection.writing'),
  };

  return (
    <div className="p-3 border border-gray-200 rounded-md bg-gray-50 mb-3">
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-x-4 gap-y-2 relative">
        <ItemActions onRemove={() => onRemove(item.id)} className="sm:col-span-3 md:col-auto" />
        <Input 
            label={t('personalSkillsSection.language')}
            value={item.language} 
            onChange={e => handleChange('language', e.target.value)} 
            containerClassName="sm:col-span-2 md:col-span-3 mb-2"
            required
        />
        <Select
            label={languageSkillSubLabels.listening}
            value={item.listening}
            onChange={(e: ChangeEvent<HTMLSelectElement>) => handleChange('listening', e.target.value as SkillLevelKey)}
            options={skillLevelOptions}
            placeholder={t('placeholders.selectLevel')}
            containerClassName="mb-1"
        />
        <Select
            label={languageSkillSubLabels.reading}
            value={item.reading}
            onChange={(e: ChangeEvent<HTMLSelectElement>) => handleChange('reading', e.target.value as SkillLevelKey)}
            options={skillLevelOptions}
            placeholder={t('placeholders.selectLevel')}
            containerClassName="mb-1"
        />
        <Select // Spoken Interaction
            label={languageSkillSubLabels.spokenInteraction}
            value={item.spokenInteraction}
            onChange={(e: ChangeEvent<HTMLSelectElement>) => handleChange('spokenInteraction', e.target.value as SkillLevelKey)}
            options={skillLevelOptions}
            placeholder={t('placeholders.selectLevel')}
            containerClassName="mb-1"
        />
        <Select // Spoken Production
            label={languageSkillSubLabels.spokenProduction}
            value={item.spokenProduction}
            onChange={(e: ChangeEvent<HTMLSelectElement>) => handleChange('spokenProduction', e.target.value as SkillLevelKey)}
            options={skillLevelOptions}
            placeholder={t('placeholders.selectLevel')}
            containerClassName="mb-1"
        />
        <Select // Writing
            label={languageSkillSubLabels.writing}
            value={item.writing}
            onChange={(e: ChangeEvent<HTMLSelectElement>) => handleChange('writing', e.target.value as SkillLevelKey)}
            options={skillLevelOptions}
            placeholder={t('placeholders.selectLevel')}
            containerClassName="mb-1"
        />
      </div>
    </div>
  );
};


const PersonalSkillsSection: React.FC<PersonalSkillsSectionProps> = ({ data, onChange }) => {
  const { t } = useLanguage();
  
  return (
    <div className="space-y-6">
      <Input 
        label={t('personalSkillsSection.motherTongue')} 
        value={data.motherTongue || ''} 
        onChange={e => onChange('motherTongue', e.target.value)} 
        placeholder={t('personalSkillsSection.placeholderMotherTongue')}
      />

      <RepeatableFieldSection
        items={data.otherLanguages}
        setItems={(actionOrValue) => { // setItems can receive a value or a function
            const newItems = typeof actionOrValue === 'function' 
                ? actionOrValue(data.otherLanguages) 
                : actionOrValue;
            onChange('otherLanguages', newItems);
        }}
        renderItem={(item, index, itemChange, itemRemove) => (
          <LanguageSkillItem item={item} onChange={itemChange} onRemove={itemRemove} />
        )}
        createEmptyItem={createEmptyLanguageSkill}
        title={t('personalSkillsSection.otherLanguages')}
        addItemText={t('personalSkillsSection.addLanguage')}
      />

      <Textarea label={t('personalSkillsSection.communicationSkills')} value={data.communicationSkills || ''} onChange={e => onChange('communicationSkills', e.target.value)} rows={3} placeholder={t('placeholders.enterHere')} />
      <Textarea label={t('personalSkillsSection.organisationalOrManagerialSkills')} value={data.organisationalOrManagerialSkills || ''} onChange={e => onChange('organisationalOrManagerialSkills', e.target.value)} rows={3} placeholder={t('placeholders.enterHere')} />
      <Textarea label={t('personalSkillsSection.professionalSkills')} value={data.professionalSkills || ''} onChange={e => onChange('professionalSkills', e.target.value)} rows={3} placeholder={t('placeholders.enterHere')}/>
      <Textarea label={t('personalSkillsSection.digitalSkills')} value={data.digitalSkills || ''} onChange={e => onChange('digitalSkills', e.target.value)} rows={3} placeholder={t('placeholders.enterHere')} />
      <Textarea label={t('personalSkillsSection.artisticSkills')} value={data.artisticSkills || ''} onChange={e => onChange('artisticSkills', e.target.value)} rows={3} placeholder={t('placeholders.enterHere')}/>
      <Textarea label={t('personalSkillsSection.otherSkills')} value={data.otherSkills || ''} onChange={e => onChange('otherSkills', e.target.value)} rows={3} placeholder={t('placeholders.enterHere')}/>
    </div>
  );
};

export default PersonalSkillsSection;
