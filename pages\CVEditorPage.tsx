
import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useCV } from '../contexts/CVContext';
import CVForm from '../components/CVForm/CVForm';
import { CVData } from '../types';
import toast from 'react-hot-toast';
import { ArrowLeftIcon } from 'lucide-react';
import Button from '../components/common/Button';
import { useLanguage } from '../contexts/LanguageContext';
import { v4 as uuidv4 } from 'uuid'; // For temporary preview ID

const CVEditorPage: React.FC = () => {
  const { cvId } = useParams<{ cvId?: string }>();
  const navigate = useNavigate();
  const { getCV, addCV, updateCV } = useCV();
  const [initialData, setInitialData] = useState<CVData | undefined>(undefined);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const { t } = useLanguage();

  useEffect(() => {
    const loadCV = async () => {
      if (cvId) {
        setIsLoading(true);
        try {
          const cv = await getCV(cvId);
          if (cv) {
            setInitialData(cv);
          } else {
            toast.error(t('cvEditor.notFound'));
            navigate('/'); 
          }
        } catch (error) {
          toast.error(t('cvEditor.loadError'));
          navigate('/');
        } finally {
          setIsLoading(false);
        }
      } else {
        setIsLoading(false);
      }
    };
    
    loadCV();
  }, [cvId, getCV, navigate, t]);

  const handleSave = async (cvData: CVData) => {
    try {
      if (cvId) { 
        await updateCV(cvData);
        toast.success(t('cvEditor.saveSuccess', {cvName: cvData.title, action: t('cvEditor.actionUpdated')}));
      } else { 
        const newCV = await addCV(cvData);
        toast.success(t('cvEditor.saveSuccess', {cvName: newCV.title, action: t('cvEditor.actionCreated')}));
      }
      navigate('/'); 
    } catch (error) {
      toast.error(t('cvEditor.saveError'));
    }
  };
  
  const handlePreview = (cvData: CVData) => {
    // For previewing, always use a consistent ID structure.
    // If it's an existing CV being edited, use its ID.
    // If it's a new, unsaved CV, generate a temporary ID for the preview session.
    const previewId = cvId || cvData.id || `temp-${uuidv4()}`;
    
    // Store the current state of cvData (which might be unsaved) in sessionStorage
    // This allows PDFPreviewPage to load the exact data from the form, even if not persisted via useCV context yet.
    const dataToPreview = { ...cvData, id: previewId }; // Ensure the data has the ID being used for navigation
    sessionStorage.setItem('previewCVData', JSON.stringify(dataToPreview));
    
    // Navigate to the preview page with a query param to indicate it's using session storage data.
    navigate(`/preview/${previewId}?temp=true`);
  };


  if (isLoading && cvId) { 
    return <div className="text-center py-10">{t('cvEditor.loading')}</div>;
  }
  
  if (cvId && !initialData && !isLoading) {
    return <div className="text-center py-10 text-red-500">{t('cvEditor.errorNotFound')}</div>;
  }

  return (
    <div className="max-w-5xl mx-auto">
       <Button 
        variant="ghost" 
        onClick={() => navigate('/')} 
        className="mb-4 text-primary-600 hover:text-primary-700"
        leftIcon={<ArrowLeftIcon size={18} />}
      >
        {t('cvEditor.backToDashboard')}
      </Button>
      <h2 className="text-3xl font-semibold text-gray-800 mb-6">
        {cvId ? t('cvEditor.editTitle', { cvName: initialData?.title || ''}) : t('cvEditor.createTitle')}
      </h2>
      <CVForm initialData={initialData} onSave={handleSave} onPreview={handlePreview} />
    </div>
  );
};

export default CVEditorPage;
