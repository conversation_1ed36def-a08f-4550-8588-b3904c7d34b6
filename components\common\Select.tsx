import React from 'react';

interface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  label?: string;
  options: { value: string | number; label: string }[];
  containerClassName?: string;
  placeholder?: string; // Added placeholder to component props
}

const Select: React.FC<SelectProps> = ({ 
  label, 
  id, 
  options, 
  containerClassName = 'mb-4', 
  placeholder, // Destructured placeholder
  ...restProps // Collect remaining props
}) => {
  return (
    <div className={containerClassName}>
      {label && <label htmlFor={id || restProps.name} className="block text-sm font-medium text-gray-700 mb-1">{label}</label>}
      <select
        id={id || restProps.name}
        className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md bg-white shadow-sm"
        {...restProps} // Spread restProps, which no longer includes placeholder
      >
        {/* Use the destructured placeholder for the initial default/empty option */}
        {placeholder && <option value="">{placeholder}</option>}
        {options.map(option => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    </div>
  );
};

export default Select;