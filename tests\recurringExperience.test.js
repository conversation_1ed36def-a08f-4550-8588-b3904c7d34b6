/**
 * Test per verificare la funzionalità delle esperienze ricorrenti
 * Questo test può essere eseguito manualmente per verificare che tutto funzioni
 */

// Dati di test per un'esperienza ricorrente
const testRecurringExperience = {
  id: "test-recurring-1",
  startDate: "2000-06", // Formato YYYY-MM
  endDate: "2010-09",   // Formato YYYY-MM
  current: false,
  occupationOrPositionHeld: "Animatore turistico",
  employer: "Resort Mediterraneo",
  employerAddress: "Via del Mare 123, Rimini",
  typeOfBusinessOrSector: "Turismo e Intrattenimento",
  mainActivitiesAndResponsibilities: "Organizzazione attività ricreative per ospiti del resort\nAnimazione serale e diurna\nCoordinamento eventi speciali",
  
  // Campi per esperienze ricorrenti
  isRecurring: true,
  displayAsGroup: true,
  recurringPattern: {
    startYear: 2000,
    endYear: 2010,
    startMonth: 6, // Giugno
    endMonth: 9,   // Settembre
    description: "Lavoro estivo stagionale"
  }
};

// Test di validazione
function testValidation() {
  console.log("🧪 Test di Validazione");
  
  // Test 1: Pattern valido
  const validPattern = testRecurringExperience.recurringPattern;
  console.log("✅ Pattern valido:", validPattern);
  
  // Test 2: Pattern con errori
  const invalidPattern = {
    startYear: 2010,
    endYear: 2000, // Errore: anno fine prima di anno inizio
    startMonth: 13, // Errore: mese non valido
    endMonth: 5     // Errore: mese fine prima di mese inizio
  };
  console.log("❌ Pattern non valido:", invalidPattern);
  
  return true;
}

// Test di espansione
function testExpansion() {
  console.log("\n🧪 Test di Espansione");
  
  const pattern = testRecurringExperience.recurringPattern;
  const expectedPeriods = [];
  
  for (let year = pattern.startYear; year <= pattern.endYear; year++) {
    expectedPeriods.push({
      startDate: `${year}-${pattern.startMonth.toString().padStart(2, '0')}`,
      endDate: `${year}-${pattern.endMonth.toString().padStart(2, '0')}`,
      year: year
    });
  }
  
  console.log("📅 Periodi attesi:", expectedPeriods.length);
  console.log("📅 Primo periodo:", expectedPeriods[0]);
  console.log("📅 Ultimo periodo:", expectedPeriods[expectedPeriods.length - 1]);
  
  return expectedPeriods.length === 11; // 2000-2010 = 11 anni
}

// Test di formattazione
function testFormatting() {
  console.log("\n🧪 Test di Formattazione");
  
  const pattern = testRecurringExperience.recurringPattern;
  
  // Simulazione della funzione di formattazione
  const formatRecurringPattern = (pattern) => {
    const months = [
      'Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno',
      'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'
    ];
    
    const startMonthName = months[pattern.startMonth - 1];
    const endMonthName = months[pattern.endMonth - 1];
    
    return `${pattern.startYear}-${pattern.endYear} (${startMonthName}-${endMonthName} ogni anno)`;
  };
  
  const formatted = formatRecurringPattern(pattern);
  console.log("📝 Formato visualizzazione:", formatted);
  
  return formatted.includes("2000-2010") && formatted.includes("Giugno-Settembre");
}

// Test di calcolo statistiche
function testStatistics() {
  console.log("\n🧪 Test di Statistiche");
  
  const pattern = testRecurringExperience.recurringPattern;
  
  const totalYears = pattern.endYear - pattern.startYear + 1;
  const monthsPerYear = pattern.endMonth - pattern.startMonth + 1;
  const totalMonths = totalYears * monthsPerYear;
  
  console.log("📊 Anni totali:", totalYears);
  console.log("📊 Mesi per anno:", monthsPerYear);
  console.log("📊 Mesi totali:", totalMonths);
  
  return totalYears === 11 && monthsPerYear === 4 && totalMonths === 44;
}

// Test di compatibilità formato date
function testDateFormat() {
  console.log("\n🧪 Test di Formato Date");
  
  const experience = testRecurringExperience;
  
  // Verifica che le date siano nel formato YYYY-MM
  const dateRegex = /^\d{4}-\d{2}$/;
  const startDateValid = dateRegex.test(experience.startDate);
  const endDateValid = dateRegex.test(experience.endDate);
  
  console.log("📅 Start date format:", experience.startDate, startDateValid ? "✅" : "❌");
  console.log("📅 End date format:", experience.endDate, endDateValid ? "✅" : "❌");
  
  // Test di conversione per visualizzazione PDF
  const formatForPDF = (dateStr) => {
    const [year, month] = dateStr.split('-');
    return `${month}/${year}`;
  };
  
  const pdfStartDate = formatForPDF(experience.startDate);
  const pdfEndDate = formatForPDF(experience.endDate);
  
  console.log("📄 PDF start date:", pdfStartDate);
  console.log("📄 PDF end date:", pdfEndDate);
  
  return startDateValid && endDateValid && pdfStartDate === "06/2000" && pdfEndDate === "09/2010";
}

// Test di serializzazione JSON
function testSerialization() {
  console.log("\n🧪 Test di Serializzazione JSON");

  const experience = testRecurringExperience;

  // Serializza e deserializza
  const serialized = JSON.stringify(experience, null, 2);
  const deserialized = JSON.parse(serialized);

  console.log("💾 Serializzazione completata");
  console.log("🔄 Deserializzazione completata");

  // Verifica che tutti i campi siano preservati
  const hasAllFields =
    deserialized.isRecurring === true &&
    deserialized.displayAsGroup === true &&
    deserialized.recurringPattern &&
    deserialized.recurringPattern.startYear === 2000 &&
    deserialized.recurringPattern.endYear === 2010;

  console.log("✅ Tutti i campi preservati:", hasAllFields ? "Sì" : "No");

  return hasAllFields;
}

// Test di modifica esperienza ricorrente
function testEditingRecurringExperience() {
  console.log("\n🧪 Test di Modifica Esperienza Ricorrente");

  const originalExperience = { ...testRecurringExperience };

  // Simula una modifica
  const modifiedExperience = {
    ...originalExperience,
    recurringPattern: {
      ...originalExperience.recurringPattern,
      endYear: 2012, // Estende di 2 anni
      description: "Lavoro estivo stagionale - aggiornato"
    },
    mainActivitiesAndResponsibilities: originalExperience.mainActivitiesAndResponsibilities + "\nNuove responsabilità aggiunte"
  };

  // Aggiorna le date di conseguenza
  modifiedExperience.endDate = "2012-09";

  console.log("📝 Esperienza originale:", originalExperience.recurringPattern.endYear);
  console.log("📝 Esperienza modificata:", modifiedExperience.recurringPattern.endYear);
  console.log("📝 Nuova descrizione:", modifiedExperience.recurringPattern.description);

  // Verifica che l'ID sia rimasto lo stesso (modifica, non creazione)
  const sameId = originalExperience.id === modifiedExperience.id;
  console.log("🆔 Stesso ID preservato:", sameId ? "Sì" : "No");

  // Verifica che i campi ricorrenti siano ancora presenti
  const stillRecurring =
    modifiedExperience.isRecurring === true &&
    modifiedExperience.displayAsGroup === true &&
    modifiedExperience.recurringPattern !== undefined;

  console.log("🔄 Ancora ricorrente:", stillRecurring ? "Sì" : "No");

  // Calcola nuove statistiche
  const newTotalYears = modifiedExperience.recurringPattern.endYear - modifiedExperience.recurringPattern.startYear + 1;
  console.log("📊 Nuovi anni totali:", newTotalYears);

  return sameId && stillRecurring && newTotalYears === 13;
}

// Esegui tutti i test
function runAllTests() {
  console.log("🚀 Avvio Test Esperienze Ricorrenti\n");
  
  const tests = [
    { name: "Validazione", fn: testValidation },
    { name: "Espansione", fn: testExpansion },
    { name: "Formattazione", fn: testFormatting },
    { name: "Statistiche", fn: testStatistics },
    { name: "Formato Date", fn: testDateFormat },
    { name: "Serializzazione", fn: testSerialization },
    { name: "Modifica Esperienza", fn: testEditingRecurringExperience }
  ];
  
  let passed = 0;
  let failed = 0;
  
  tests.forEach(test => {
    try {
      const result = test.fn();
      if (result) {
        console.log(`\n✅ ${test.name}: PASSATO`);
        passed++;
      } else {
        console.log(`\n❌ ${test.name}: FALLITO`);
        failed++;
      }
    } catch (error) {
      console.log(`\n💥 ${test.name}: ERRORE -`, error.message);
      failed++;
    }
  });
  
  console.log(`\n📊 Risultati: ${passed} passati, ${failed} falliti`);
  console.log(failed === 0 ? "🎉 Tutti i test sono passati!" : "⚠️ Alcuni test sono falliti");
  
  return failed === 0;
}

// Esporta per uso in Node.js o browser
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testRecurringExperience,
    runAllTests
  };
} else {
  // Esegui automaticamente se caricato in browser
  runAllTests();
}
