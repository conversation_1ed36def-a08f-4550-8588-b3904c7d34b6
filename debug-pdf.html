<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Debug Test</title>
    <script src="https://unpkg.com/jspdf@3.0.1/dist/jspdf.umd.min.js" crossorigin="anonymous"></script>
    <script src="https://unpkg.com/jspdf-autotable@5.0.2/dist/jspdf.plugin.autotable.min.js" crossorigin="anonymous"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        #log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>PDF Generation Debug Test</h1>
    
    <div class="test-section info">
        <h3>Test Environment</h3>
        <p>This page tests jsPDF loading and functionality to debug PDF generation issues.</p>
    </div>

    <div class="test-section">
        <h3>Tests</h3>
        <button onclick="testCDNLoading()">Test CDN Loading</button>
        <button onclick="testBasicPDF()">Test Basic PDF</button>
        <button onclick="testAutoTable()">Test AutoTable</button>
        <button onclick="testESModules()">Test ES Modules</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <div class="test-section">
        <h3>Log Output</h3>
        <div id="log"></div>
    </div>

    <script type="importmap">
    {
      "imports": {
        "jspdf": "https://esm.sh/jspdf@^3.0.1",
        "jspdf-autotable": "https://esm.sh/jspdf-autotable@^5.0.2"
      }
    }
    </script>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'black';
            logEntry.textContent = `[${timestamp}] ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function testCDNLoading() {
            log('=== Testing CDN Loading ===');
            
            log('Checking window object...');
            log(`window type: ${typeof window}`);
            
            log('Checking jsPDF availability...');
            log(`window.jsPDF type: ${typeof window.jsPDF}`);
            
            if (window.jsPDF) {
                log('✓ jsPDF is available from CDN', 'success');
                
                try {
                    const doc = new window.jsPDF();
                    log('✓ jsPDF instance created successfully', 'success');
                    
                    log(`autoTable type: ${typeof doc.autoTable}`);
                    if (typeof doc.autoTable === 'function') {
                        log('✓ autoTable is available', 'success');
                    } else {
                        log('✗ autoTable is not available', 'error');
                        log(`Available methods: ${Object.getOwnPropertyNames(doc).slice(0, 10).join(', ')}...`);
                    }
                } catch (error) {
                    log(`✗ Error creating jsPDF instance: ${error.message}`, 'error');
                }
            } else {
                log('✗ jsPDF is not available from CDN', 'error');
                log(`Available window properties with 'pdf': ${Object.keys(window).filter(key => key.toLowerCase().includes('pdf')).join(', ')}`);
            }
        }

        function testBasicPDF() {
            log('=== Testing Basic PDF Generation ===');
            
            if (!window.jsPDF) {
                log('✗ jsPDF not available, cannot test', 'error');
                return;
            }

            try {
                const doc = new window.jsPDF();
                doc.text('Hello World!', 20, 20);
                doc.save('test-basic.pdf');
                log('✓ Basic PDF generated and downloaded successfully', 'success');
            } catch (error) {
                log(`✗ Error generating basic PDF: ${error.message}`, 'error');
            }
        }

        function testAutoTable() {
            log('=== Testing AutoTable ===');
            
            if (!window.jsPDF) {
                log('✗ jsPDF not available, cannot test', 'error');
                return;
            }

            try {
                const doc = new window.jsPDF();
                
                if (typeof doc.autoTable !== 'function') {
                    log('✗ autoTable not available', 'error');
                    return;
                }

                doc.autoTable({
                    head: [['Name', 'Email', 'Country']],
                    body: [
                        ['David', '<EMAIL>', 'Sweden'],
                        ['Castille', '<EMAIL>', 'Spain'],
                    ],
                    startY: 30,
                });
                
                doc.save('test-autotable.pdf');
                log('✓ AutoTable PDF generated and downloaded successfully', 'success');
            } catch (error) {
                log(`✗ Error generating AutoTable PDF: ${error.message}`, 'error');
            }
        }

        async function testESModules() {
            log('=== Testing ES Modules ===');
            
            try {
                log('Attempting to import jsPDF as ES module...');
                const jsPDFModule = await import('jspdf');
                const jsPDFClass = jsPDFModule.default || jsPDFModule.jsPDF || jsPDFModule;
                log('✓ jsPDF ES module imported successfully', 'success');
                
                const doc = new jsPDFClass();
                log('✓ jsPDF instance created from ES module', 'success');
                
                try {
                    log('Attempting to import jspdf-autotable...');
                    await import('jspdf-autotable');
                    log('✓ jspdf-autotable ES module imported successfully', 'success');
                    
                    if (typeof doc.autoTable === 'function') {
                        log('✓ autoTable is available after ES module import', 'success');
                        
                        doc.text('ES Module Test', 20, 20);
                        doc.autoTable({
                            head: [['Test', 'Result']],
                            body: [['ES Modules', 'Working']],
                            startY: 30,
                        });
                        doc.save('test-es-modules.pdf');
                        log('✓ ES Module PDF with autoTable generated successfully', 'success');
                    } else {
                        log('✗ autoTable not available after ES module import', 'error');
                    }
                } catch (autoTableError) {
                    log(`✗ Error importing jspdf-autotable: ${autoTableError.message}`, 'error');
                }
            } catch (error) {
                log(`✗ Error importing jsPDF ES module: ${error.message}`, 'error');
            }
        }

        // Run initial test on page load
        window.addEventListener('load', function() {
            log('Page loaded, running initial tests...');
            setTimeout(testCDNLoading, 100);
        });
    </script>
</body>
</html>
