
import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { CVData, PersonalInformation, PersonalSkills } from '../types';
import { v4 as uuidv4 } from 'uuid';
import { fetchCVs, fetchCV, createCV, updateCV as apiUpdateCV, deleteCV as apiDeleteCV } from '../services/apiService';
import toast from 'react-hot-toast';
import { useLanguage } from './LanguageContext';

interface CVContextType {
  cvs: CVData[];
  addCV: (cvData: Omit<CVData, 'id' | 'lastUpdate'>) => Promise<CVData>;
  updateCV: (cvData: CVData) => Promise<void>;
  deleteCV: (id: string) => Promise<void>;
  getCV: (id: string) => Promise<CVData | undefined>;
  duplicateCV: (id: string) => CVData | undefined;
  isLoading: boolean;
  error: string | null;
}

const CVContext = createContext<CVContextType | undefined>(undefined);

const createEmptyPersonalInformation = (): PersonalInformation => ({
  name: '',
  surname: '',
  photo: undefined,
  address: '',
  postalCode: '',
  city: '',
  country: '',
  telephones: [],
  emailAddresses: [],
  websites: [],
  linkedinProfile: '',
  instantMessaging: [],
  sex: '',
  dateOfBirth: '',
  nationality: '',
});

const createEmptyPersonalSkills = (): PersonalSkills => ({
  motherTongue: '',
  otherLanguages: [],
  communicationSkills: '',
  organisationalOrManagerialSkills: '',
  professionalSkills: '',
  digitalSkills: '',
  artisticSkills: '',
  otherSkills: '',
});

export const CVProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [cvs, setCvs] = useState<CVData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { t } = useLanguage();

  // Fetch CVs on component mount
  useEffect(() => {
    const loadCVs = async () => {
      try {
        setIsLoading(true);
        const data = await fetchCVs();
        setCvs(data);
      } catch (err) {
        console.error('Error loading CVs:', err);
        setError(t('errors.loadingFailed'));
        toast.error(t('errors.loadingFailed'));
      } finally {
        setIsLoading(false);
      }
    };

    loadCVs();
  }, [t]);

  const addCV = useCallback(async (cvData: Omit<CVData, 'id' | 'lastUpdate'>): Promise<CVData> => {
    try {
      const newCV = await createCV(cvData);
      setCvs(prevCvs => [...prevCvs, newCV]);
      return newCV;
    } catch (err) {
      console.error('Error creating CV:', err);
      toast.error(t('errors.saveFailed'));
      throw err;
    }
  }, [t]);

  const updateCV = useCallback(async (updatedCV: CVData) => {
    try {
      const result = await apiUpdateCV(updatedCV);
      setCvs(prevCvs => prevCvs.map(cv => (cv.id === result.id ? result : cv)));
    } catch (err) {
      console.error('Error updating CV:', err);
      toast.error(t('errors.updateFailed'));
      throw err;
    }
  }, [t]);

  const deleteCV = useCallback(async (id: string) => {
    try {
      await apiDeleteCV(id);
      setCvs(prevCvs => prevCvs.filter(cv => cv.id !== id));
    } catch (err) {
      console.error('Error deleting CV:', err);
      toast.error(t('errors.deleteFailed'));
      throw err;
    }
  }, [t]);

  const getCV = useCallback(async (id: string): Promise<CVData | undefined> => {
    try {
      // First check if we already have it in state
      const cachedCV = cvs.find(cv => cv.id === id);
      if (cachedCV) return cachedCV;
      
      // Otherwise fetch from API
      const cv = await fetchCV(id);
      return cv;
    } catch (err) {
      console.error('Error fetching CV:', err);
      return undefined;
    }
  }, [cvs]);

  const duplicateCV = useCallback((id: string): CVData | undefined => {
    const originalCV = cvs.find(cv => cv.id === id);
    if (!originalCV) return undefined;

    const duplicatedCV: CVData = JSON.parse(JSON.stringify(originalCV)); // Deep copy
    duplicatedCV.id = uuidv4();
    duplicatedCV.title = `${originalCV.title} (Copy)`; // Use title field
    duplicatedCV.lastUpdate = new Date().toISOString();
    
    // Re-assign new IDs for repeatable items if they were part of the deep copy (usually they are)
    // For this app, uuid should be unique per item, so deep copy is fine.
    // Ensure all nested structures are present
    duplicatedCV.personalInformation = { ...createEmptyPersonalInformation(), ...duplicatedCV.personalInformation };
    duplicatedCV.personalSkills = { ...createEmptyPersonalSkills(), ...duplicatedCV.personalSkills };
    duplicatedCV.workExperience = (duplicatedCV.workExperience || []).map(item => ({...item, id: uuidv4()}));
    duplicatedCV.educationTraining = (duplicatedCV.educationTraining || []).map(item => ({...item, id: uuidv4()}));
    if (duplicatedCV.personalSkills.otherLanguages) {
        duplicatedCV.personalSkills.otherLanguages = duplicatedCV.personalSkills.otherLanguages.map(lang => ({...lang, id: uuidv4()}));
    }
    if (duplicatedCV.personalInformation.instantMessaging) {
        duplicatedCV.personalInformation.instantMessaging = duplicatedCV.personalInformation.instantMessaging.map(im => ({...im, id: uuidv4()}));
    }
    if (duplicatedCV.attachments) {
        duplicatedCV.attachments = duplicatedCV.attachments.map(att => ({...att, id: uuidv4()}));
    }


    setCvs(prevCvs => [...prevCvs, duplicatedCV]);
    return duplicatedCV;
  }, [cvs]);

  return (
    <CVContext.Provider value={{ 
      cvs, 
      addCV, 
      updateCV, 
      deleteCV, 
      getCV, 
      duplicateCV,
      isLoading,
      error 
    }}>
      {children}
    </CVContext.Provider>
  );
};

export const useCV = (): CVContextType => {
  const context = useContext(CVContext);
  if (!context) {
    throw new Error('useCV must be used within a CVProvider');
  }
  return context;
};
