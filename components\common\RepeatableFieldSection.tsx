
import React from 'react';
import Button from './Button';
import { IdItem, RepeatableSectionProps } from '../../types'; // Ensure correct path
import { PlusCircleIcon, TrashIcon } from 'lucide-react';


const RepeatableFieldSection = <T extends IdItem,>({
  items,
  setItems,
  renderItem,
  createEmptyItem,
  title,
  addItemText = 'Add Item'
}: RepeatableSectionProps<T>) => {
  
  const handleAddItem = () => {
    setItems([...items, createEmptyItem()]);
  };

  const handleRemoveItem = (id: string) => {
    setItems(items.filter(item => item.id !== id));
  };

  const handleItemChange = (updatedItem: T) => {
    setItems(items.map(item => (item.id === updatedItem.id ? updatedItem : item)));
  };

  return (
    <div className="mb-6">
      <h3 className="text-xl font-semibold text-gray-700 mb-3">{title}</h3>
      {items.map((item, index) => (
        <div key={item.id} className="mb-4 p-4 border border-gray-200 rounded-md bg-gray-50 relative">
          {renderItem(item, index, handleItemChange, handleRemoveItem)}
        </div>
      ))}
      <Button type="button" onClick={handleAddItem} variant="outline" size="sm" leftIcon={<PlusCircleIcon size={16}/>}>
        {addItemText}
      </Button>
    </div>
  );
};

export default RepeatableFieldSection;

// Specific Item Actions (e.g., Remove Button for each item) - can be passed down via renderItem
export const ItemActions: React.FC<{ onRemove: () => void; className?: string }> = ({ onRemove, className }) => (
  <div className={`absolute top-2 right-2 ${className}`}>
    <Button type="button" onClick={onRemove} variant="ghost" size="sm" className="text-red-500 hover:text-red-700 hover:bg-red-50 p-1">
      <TrashIcon size={18} />
    </Button>
  </div>
);
