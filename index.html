
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>CV Pro Manager</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: {"50":"#eff6ff","100":"#dbeafe","200":"#bfdbfe","300":"#93c5fd","400":"#60a5fa","500":"#3b82f6","600":"#2563eb","700":"#1d4ed8","800":"#1e40af","900":"#1e3a8a","950":"#172554"}
          }
        }
      }
    }
  </script>

  <script src="https://unpkg.com/jspdf@3.0.1/dist/jspdf.umd.min.js" crossorigin="anonymous" onload="console.log('jsPDF CDN loaded');" onerror="console.error('jsPDF CDN failed to load');"></script>
  <script src="https://unpkg.com/jspdf-autotable@5.0.2/dist/jspdf.plugin.autotable.min.js" crossorigin="anonymous" onload="console.log('jsPDF autoTable CDN loaded');" onerror="console.error('jsPDF autoTable CDN failed to load');"></script>
  <script>
    // Debug script loading with more detailed checks
    window.addEventListener('load', function() {
      console.log('Window loaded. Checking jsPDF availability...');
      console.log('window object keys containing "jsPDF":', Object.keys(window).filter(k => k.toLowerCase().includes('jspdf')));
      console.log('window.jsPDF:', typeof window.jsPDF);
      console.log('window.jspdf:', typeof window.jspdf);

      // Check for different possible global names
      const possibleNames = ['jsPDF', 'jspdf', 'JSPDF'];
      let foundJsPDF = null;

      for (const name of possibleNames) {
        if (window[name]) {
          console.log(`Found jsPDF at window.${name}:`, typeof window[name]);
          foundJsPDF = window[name];
          break;
        }
      }

      if (foundJsPDF) {
        try {
          const testDoc = new foundJsPDF();
          console.log('jsPDF instance created successfully');
          console.log('autoTable available:', typeof testDoc.autoTable);
          console.log('Available methods on doc:', Object.getOwnPropertyNames(testDoc).filter(m => typeof testDoc[m] === 'function').slice(0, 10));
        } catch (error) {
          console.error('Error creating jsPDF instance:', error);
        }
      } else {
        console.error('jsPDF not found on window object');
        console.log('Available window properties (first 20):', Object.keys(window).slice(0, 20));
      }
    });
  </script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

<script type="importmap">
{
  "imports": {
    "react/": "https://esm.sh/react@^19.1.0/",
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react-router-dom": "https://esm.sh/react-router-dom@^7.6.2",
    "react-hot-toast": "https://esm.sh/react-hot-toast@^2.5.2",
    "uuid": "https://esm.sh/uuid@^11.1.0",
    "lucide-react": "https://esm.sh/lucide-react@^0.513.0",
    "jspdf": "https://esm.sh/jspdf@^3.0.1",
    "jspdf-autotable": "https://esm.sh/jspdf-autotable@^5.0.2"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
<body class="bg-gray-100">
  <div id="root"></div>
  <script type="module" src="/index.tsx"></script>
</body>
</html>
