
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useCV } from '../contexts/CVContext';
import Button from '../components/common/Button';
import Card from '../components/common/Card';
import { PlusCircleIcon, Edit3Icon, CopyIcon, Trash2Icon, FileTextIcon, EyeIcon } from 'lucide-react';
import toast from 'react-hot-toast';
import { useLanguage } from '../contexts/LanguageContext';

const DashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const { cvs, deleteCV, duplicateCV } = useCV();
  const { t, getLocalizedDate } = useLanguage();

  const handleNewCV = () => {
    navigate('/new');
  };

  const handleEdit = (id: string) => {
    navigate(`/edit/${id}`);
  };

  const handlePreview = (id: string) => {
    navigate(`/preview/${id}`);
  }

  const handleDuplicate = (id: string) => {
    const originalCV = cvs.find(cv => cv.id === id);
    if (!originalCV) {
        toast.error(t('dashboard.duplicateError'));
        return;
    }
    const duplicated = duplicateCV(id);
    if (duplicated) {
      toast.success(t('dashboard.duplicateSuccess', { cvName: duplicated.title })); // Use title
    } else {
      toast.error(t('dashboard.duplicateError'));
    }
  };

  const handleDelete = (id: string, cvTitle: string) => { // Use cvTitle
    if (window.confirm(t('dashboard.confirmDelete', { cvName: cvTitle }))) { // Use cvTitle
      deleteCV(id);
      toast.success(t('dashboard.deleteSuccess', {cvName: cvTitle})); // Use cvTitle
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-semibold text-gray-800">{t('dashboard.title')}</h2>
        <Button onClick={handleNewCV} variant="primary" leftIcon={<PlusCircleIcon size={20}/>}>
          {t('dashboard.createNewCV')}
        </Button>
      </div>

      {cvs.length === 0 ? (
        <Card>
          <div className="text-center py-12">
            <FileTextIcon size={48} className="mx-auto text-gray-400 mb-4" />
            <h3 className="text-xl font-medium text-gray-700">{t('dashboard.noCVsFound')}</h3>
            <p className="text-gray-500 mt-2">{t('dashboard.getStarted')}</p>
            <Button onClick={handleNewCV} variant="primary" className="mt-6" leftIcon={<PlusCircleIcon size={20}/>}>
              {t('dashboard.createNewCV')}
            </Button>
          </div>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {cvs.sort((a,b) => new Date(b.lastUpdate).getTime() - new Date(a.lastUpdate).getTime()).map(cv => (
            <Card key={cv.id} className="flex flex-col justify-between">
              <div>
                <h3 className="text-xl font-semibold text-primary-700 truncate mb-1">{cv.title}</h3> {/* Use cv.title */}
                <p className="text-sm text-gray-500 mb-3">
                  {t('dashboard.lastUpdated', { date: getLocalizedDate(cv.lastUpdate, {month: 'short', day:'numeric', year:'numeric', hour:'2-digit', minute:'2-digit'}) })}
                </p>
                <p className="text-sm text-gray-600">
                  {t('dashboard.candidate', {name: `${cv.personalInformation.name || ''} ${cv.personalInformation.surname || ''}`.trim()})} {/* Use name and surname */}
                </p>
              </div>
              <div className="mt-4 pt-4 border-t border-gray-200 flex flex-wrap gap-2 justify-end">
                <Button onClick={() => handleEdit(cv.id)} variant="outline" size="sm" leftIcon={<Edit3Icon size={16}/>}>{t('dashboard.actions.edit')}</Button>
                <Button onClick={() => handlePreview(cv.id)} variant="outline" size="sm" leftIcon={<EyeIcon size={16}/>}>{t('dashboard.actions.preview')}</Button>
                <Button onClick={() => handleDuplicate(cv.id)} variant="ghost" size="sm" leftIcon={<CopyIcon size={16}/>}>{t('dashboard.actions.duplicate')}</Button>
                <Button onClick={() => handleDelete(cv.id, cv.title)} variant="ghost" size="sm" className="text-red-600 hover:bg-red-50" leftIcon={<Trash2Icon size={16}/>}>{t('dashboard.actions.delete')}</Button> {/* Use cv.title */}
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default DashboardPage;
