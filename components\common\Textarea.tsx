import React from 'react';

interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  containerClassName?: string;
}

const Textarea: React.FC<TextareaProps> = ({ label, id, containerClassName = 'mb-4', ...props }) => {
  return (
    <div className={containerClassName}>
      {label && <label htmlFor={id || props.name} className="block text-sm font-medium text-gray-700 mb-1">{label}</label>}
      <textarea
        id={id || props.name}
        rows={3}
        className="mt-1 block w-full px-3 py-2 bg-white text-gray-900 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
        {...props}
      />
    </div>
  );
};

export default Textarea;