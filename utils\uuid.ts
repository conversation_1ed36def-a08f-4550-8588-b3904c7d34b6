
// This file is to ensure uuid is consistently available if needed.
// For this project, direct import of v4 from 'uuid' in contexts/CVContext.tsx is fine.
// If you wanted to abstract or use a different UUID library later, this would be the place.
// For now, it's mostly a placeholder for good practice.
// You would need to install `uuid` and `@types/uuid` if you were bundling.
// Since this project uses CDN for major libraries and assumes build tools for React/TS,
// we'll rely on the direct import in CVContext.tsx working (e.g. via Vite, CRA, or similar setup).
// For a pure CDN-based demo without build tools, a global UUID function would be needed or use crypto.randomUUID().

// Let's use crypto.randomUUID() which is standard in modern browsers and Node.js
// This avoids needing to add 'uuid' to package.json for simpler setups.

export const generateUUID = (): string => {
    if (typeof crypto !== 'undefined' && crypto.randomUUID) {
      return crypto.randomUUID();
    }
    // Fallback for older environments (less likely with React 18+ focus)
    // A more robust fallback would use a polyfill or a simple Math.random based one (not truly UUID v4)
    // For simplicity, we'll assume crypto.randomUUID is available.
    // If not, the direct 'uuid' package import in CVContext should be preferred if a build system is present.
    console.warn("crypto.randomUUID not available. Consider using the 'uuid' package for robust UUIDs.");
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
};

// In CVContext.tsx and other places, `import { v4 as uuidv4 } from 'uuid';` is used.
// This assumes `uuid` is part of the project's dependencies.
// If `uuid` package is not available, and `crypto.randomUUID()` is preferred globally:
// you'd replace `uuidv4()` with `generateUUID()` from this file.
// Given the structure, it's likely a build tool is assumed, making `uuid` package feasible.
// I will keep the `uuid` package usage as it's common in React projects.
// This file can be removed if `uuid` package is used directly and consistently.
// For this deliverable, I will remove direct `uuid` import from CVContext.tsx and use `generateUUID` from here.
// However, the prompt doesn't ask to install packages. `crypto.randomUUID()` is best for no-install.
// I will modify CVContext and other sections to use `generateUUID` from this util, relying on `crypto.randomUUID()`.

// Re-evaluating: The prompt is to produce React code. `uuid` is a very common library in React ecosystems.
// Assuming a standard React setup (like Vite or CRA) would make `uuid` available after `npm install uuid @types/uuid`.
// Since the prompt doesn't specify NO external npm packages, just "handful of files", using `uuid` is idiomatic.
// I'll stick to `import { v4 as uuidv4 } from 'uuid';` as shown in `CVContext.tsx` earlier, assuming it's available.
// This `utils/uuid.ts` file becomes less critical if direct import of `uuid` is used.
// I'll remove this file from the final list and ensure `CVContext.tsx` etc. directly `import { v4 as uuidv4 } from 'uuid';`
// This change is not made to current files as it's a meta-commentary on UUID strategy.
// The provided solution for `CVContext.tsx` already uses `import { v4 as uuidv4 } from 'uuid';`. That's the chosen path.
// This file is therefore NOT included in the final XML output.

// --- FINAL DECISION: Stick with `import { v4 as uuidv4 } from 'uuid';` in files needing it. ---
// This means `uuid` and `@types/uuid` would be dev dependencies.
// The XML does not include `package.json`, so this is an implicit requirement.
// If `uuid` is not allowed, then `crypto.randomUUID()` is the alternative.
// The current code uses `uuid` directly.
