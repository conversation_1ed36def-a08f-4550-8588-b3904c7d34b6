<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimal PDF Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        #log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>Minimal PDF Test</h1>
    
    <button onclick="testESModules()">Test ES Modules Only</button>
    <button onclick="testCDN()">Test CDN Only</button>
    <button onclick="clearLog()">Clear Log</button>
    
    <div id="log"></div>

    <script type="importmap">
    {
      "imports": {
        "jspdf": "https://esm.sh/jspdf@^3.0.1",
        "jspdf-autotable": "https://esm.sh/jspdf-autotable@^5.0.2"
      }
    }
    </script>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'black';
            logEntry.textContent = `[${timestamp}] ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        async function testESModules() {
            log('=== Testing ES Modules (Improved Method) ===');

            try {
                log('Loading jsPDF and autoTable together...');

                // Import jsPDF
                const jsPDFModule = await import('jspdf');
                log('jsPDF module imported successfully');
                const jsPDFClass = jsPDFModule.default || jsPDFModule.jsPDF || jsPDFModule;
                log('jsPDF class extracted:', typeof jsPDFClass);

                // Import and apply autoTable plugin
                const autoTableModule = await import('jspdf-autotable');
                log('autoTable module imported successfully');
                log('autoTable module keys:', Object.keys(autoTableModule).join(', '));

                // Apply the plugin to the jsPDF class
                if (autoTableModule.default && typeof autoTableModule.default === 'function') {
                    log('Applying autoTable plugin to jsPDF class...');
                    autoTableModule.default(jsPDFClass);
                } else {
                    log('autoTable module structure:', Object.keys(autoTableModule));
                    // Try different ways to apply the plugin
                    if (typeof autoTableModule === 'function') {
                        log('Applying autoTable as function...');
                        autoTableModule(jsPDFClass);
                    } else if (autoTableModule.applyPlugin) {
                        log('Applying autoTable using applyPlugin...');
                        autoTableModule.applyPlugin(jsPDFClass);
                    }
                }

                // Test if autoTable is now available
                log('Creating jsPDF instance after plugin application...');
                const doc = new jsPDFClass();
                log('autoTable available on instance:', typeof doc.autoTable);

                if (typeof doc.autoTable === 'function') {
                    log('✓ autoTable successfully applied to jsPDF class');

                    log('Testing PDF generation with autoTable...');
                    doc.text('Test PDF with ES Modules (Improved)', 20, 20);

                    doc.autoTable({
                        head: [['Name', 'Value']],
                        body: [
                            ['ES Modules', 'Working'],
                            ['autoTable', 'Available'],
                            ['Method', 'Improved']
                        ],
                        startY: 40,
                    });

                    doc.save('test-es-modules-improved.pdf');
                    log('✓ PDF generated successfully with ES modules!', 'success');
                } else {
                    log('✗ autoTable still not available after plugin application', 'error');
                }

            } catch (error) {
                log(`✗ Error with ES modules: ${error.message}`, 'error');
                console.error('Full error:', error);
            }
        }

        async function testCDN() {
            log('=== Testing CDN (if available) ===');
            
            // Load CDN scripts dynamically
            try {
                log('Loading jsPDF from CDN...');
                await loadScript('https://unpkg.com/jspdf@3.0.1/dist/jspdf.umd.min.js');
                log('jsPDF CDN script loaded');
                
                log('Loading jspdf-autotable from CDN...');
                await loadScript('https://unpkg.com/jspdf-autotable@5.0.2/dist/jspdf.plugin.autotable.min.js');
                log('jspdf-autotable CDN script loaded');
                
                // Check if jsPDF is available
                if (window.jsPDF) {
                    log('window.jsPDF is available');
                    const doc = new window.jsPDF();
                    log('jsPDF instance created from CDN');
                    log('autoTable available:', typeof doc.autoTable);
                    
                    if (typeof doc.autoTable === 'function') {
                        doc.text('Test PDF with CDN', 20, 20);
                        
                        doc.autoTable({
                            head: [['Name', 'Value']],
                            body: [
                                ['CDN', 'Working'],
                                ['autoTable', 'Available']
                            ],
                            startY: 40,
                        });
                        
                        doc.save('test-cdn.pdf');
                        log('✓ PDF generated successfully with CDN!', 'success');
                    } else {
                        log('✗ autoTable not available from CDN', 'error');
                    }
                } else {
                    log('✗ window.jsPDF not available after CDN load', 'error');
                }
                
            } catch (error) {
                log(`✗ Error with CDN: ${error.message}`, 'error');
                console.error('Full error:', error);
            }
        }

        function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = resolve;
                script.onerror = reject;
                document.head.appendChild(script);
            });
        }

        // Test on page load
        window.addEventListener('load', function() {
            log('Page loaded. Ready for testing.');
        });
    </script>
</body>
</html>
