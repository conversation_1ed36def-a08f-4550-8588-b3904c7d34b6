<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test PDF Component</title>
    <script src="https://unpkg.com/jspdf@3.0.1/dist/jspdf.umd.min.js" crossorigin="anonymous"></script>
    <script src="https://unpkg.com/jspdf-autotable@5.0.2/dist/jspdf.plugin.autotable.min.js" crossorigin="anonymous"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        #log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>Test PDF Generation with Sample CV Data</h1>
    
    <button onclick="testPDFGeneration()">Test PDF Generation</button>
    <button onclick="clearLog()">Clear Log</button>
    
    <div id="log"></div>

    <script type="importmap">
    {
      "imports": {
        "jspdf": "https://esm.sh/jspdf@^3.0.1",
        "jspdf-autotable": "https://esm.sh/jspdf-autotable@^5.0.2"
      }
    }
    </script>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'black';
            logEntry.textContent = `[${timestamp}] ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // Sample CV data
        const sampleCVData = {
            title: "Test CV",
            personalInformation: {
                name: "John",
                surname: "Doe",
                address: "123 Test Street",
                postalCode: "12345",
                city: "Test City",
                country: "Test Country",
                telephones: ["+1234567890"],
                emailAddresses: ["<EMAIL>"],
                websites: [],
                linkedinProfile: "",
                instantMessaging: [],
                sex: "M",
                dateOfBirth: "1990-01-01",
                nationality: "Test Nationality"
            },
            desiredEmploymentOrPersonalStatement: "Test personal statement",
            workExperience: [
                {
                    id: "1",
                    startDate: "2020-01",
                    endDate: "2023-12",
                    current: false,
                    occupationOrPositionHeld: "Test Position",
                    employer: "Test Company",
                    employerAddress: "Test Address",
                    city: "Test City",
                    country: "Test Country",
                    typeOfBusinessOrSector: "Test Sector",
                    mainActivitiesAndResponsibilities: "Test responsibilities",
                    isRecurring: false,
                    displayAsGroup: false
                }
            ],
            educationTraining: [
                {
                    id: "1",
                    startDate: "2015-09",
                    endDate: "2019-06",
                    current: false,
                    titleOfQualificationAwarded: "Test Degree",
                    organisationName: "Test University",
                    organisationAddress: "Test Address",
                    mainSubjectsOrSkillsCovered: "Test subjects",
                    levelInNationalOrInternationalClassification: "Bachelor"
                }
            ],
            personalSkills: {
                motherTongue: "English",
                otherLanguages: [
                    {
                        id: "1",
                        language: "Spanish",
                        listening: "B2",
                        reading: "B2",
                        spokenInteraction: "B1",
                        spokenProduction: "B1",
                        writing: "B1"
                    }
                ],
                communicationSkills: "Excellent communication skills",
                organisationalOrManagerialSkills: "Strong organizational skills",
                professionalSkills: "Professional skills description",
                digitalSkills: "Digital skills description",
                artisticSkills: "Artistic skills description",
                otherSkills: "Other skills description"
            },
            drivingLicence: "B",
            additionalInformation: "Additional information",
            attachments: []
        };

        // Simple translation function for testing
        const t = (key, replacements = {}) => {
            const translations = {
                'pdf.personalInformationTitle': 'PERSONAL INFORMATION',
                'pdf.desiredEmploymentTitle': 'DESIRED EMPLOYMENT',
                'pdf.workExperienceTitle': 'WORK EXPERIENCE',
                'pdf.educationTrainingTitle': 'EDUCATION AND TRAINING',
                'pdf.personalSkillsTitle': 'PERSONAL SKILLS',
                'pdf.motherTongueTitle': 'Mother tongue',
                'pdf.otherLanguagesTitle': 'Other languages',
                'pdf.communicationSkills': 'Communication skills',
                'pdf.organisationalOrManagerialSkills': 'Organisational / managerial skills',
                'pdf.professionalSkills': 'Professional skills',
                'pdf.digitalSkills': 'Digital skills',
                'pdf.artisticSkills': 'Artistic skills',
                'pdf.otherSkills': 'Other skills',
                'pdf.drivingLicenceTitle': 'DRIVING LICENCE',
                'pdf.additionalInformationTitle': 'ADDITIONAL INFORMATION',
                'pdf.attachmentsTitle': 'ATTACHMENTS'
            };
            
            let result = translations[key] || key;
            
            // Simple replacement logic
            if (replacements) {
                Object.keys(replacements).forEach(replaceKey => {
                    result = result.replace(`{${replaceKey}}`, replacements[replaceKey]);
                });
            }
            
            return result;
        };

        // Simple date formatting function
        const getLocalizedDate = (dateString, options = {}) => {
            if (!dateString) return '';
            try {
                const date = new Date(dateString);
                return date.toLocaleDateString('en-US', options);
            } catch (error) {
                return dateString;
            }
        };

        // Simplified version of the PDF generation function
        async function generateTestPDF(cvData, t, getLocalizedDate) {
            log('Starting PDF generation test...');

            // Get jsPDF (CDN or ES module)
            let jsPDFClass;
            if (typeof window !== 'undefined') {
                const possibleNames = ['jsPDF', 'jspdf', 'JSPDF'];
                let foundJsPDF = null;

                for (const name of possibleNames) {
                    if (window[name]) {
                        log(`Using jsPDF from CDN (window.${name})`);
                        foundJsPDF = window[name];
                        break;
                    }
                }

                if (foundJsPDF) {
                    jsPDFClass = foundJsPDF;
                } else {
                    try {
                        log('CDN not available, trying ES module import...');
                        const jsPDFModule = await import('jspdf');
                        jsPDFClass = jsPDFModule.default || jsPDFModule.jsPDF || jsPDFModule;
                        log('Successfully imported jsPDF as ES module');
                    } catch (error) {
                        log(`Failed to import jsPDF: ${error.message}`, 'error');
                        throw new Error('jsPDF could not be loaded');
                    }
                }
            }
            
            log('Creating jsPDF instance...');
            const doc = new jsPDFClass({
                orientation: 'p',
                unit: 'pt',
                format: 'a4'
            });

            // Check autoTable availability
            if (typeof doc.autoTable !== 'function') {
                try {
                    log('autoTable not available, trying to import jspdf-autotable...');
                    const autoTableModule = await import('jspdf-autotable');
                    log('Successfully imported jspdf-autotable module');

                    // Apply the plugin to the jsPDF class
                    if (autoTableModule.default) {
                        log('Applying autoTable plugin to jsPDF class...');
                        autoTableModule.default(jsPDFClass);
                    } else if (typeof autoTableModule === 'function') {
                        log('Applying autoTable plugin function to jsPDF class...');
                        autoTableModule(jsPDFClass);
                    }

                    log('autoTable available after plugin application:', typeof doc.autoTable === 'function');
                } catch (error) {
                    log(`Failed to import jspdf-autotable: ${error.message}`, 'error');
                    throw new Error('jspdf-autotable could not be loaded');
                }
            }

            if (typeof doc.autoTable !== 'function') {
                throw new Error('autoTable is still not available after import attempts');
            }
            
            log('jsPDF and autoTable are ready');

            // Simple PDF content
            doc.setFontSize(16);
            doc.text(cvData.title || 'Test CV', 50, 50);
            
            doc.setFontSize(12);
            doc.text(`${cvData.personalInformation.name} ${cvData.personalInformation.surname}`, 50, 80);
            doc.text(cvData.personalInformation.emailAddresses[0] || 'No email', 50, 100);
            
            // Test autoTable
            if (cvData.workExperience && cvData.workExperience.length > 0) {
                doc.autoTable({
                    head: [['Position', 'Company', 'Period']],
                    body: cvData.workExperience.map(exp => [
                        exp.occupationOrPositionHeld,
                        exp.employer,
                        `${exp.startDate} - ${exp.endDate || 'Present'}`
                    ]),
                    startY: 130,
                });
            }
            
            doc.save('test-cv.pdf');
            log('PDF generated and downloaded successfully!', 'success');
        }

        async function testPDFGeneration() {
            try {
                await generateTestPDF(sampleCVData, t, getLocalizedDate);
            } catch (error) {
                log(`Error generating PDF: ${error.message}`, 'error');
                console.error('Full error:', error);
            }
        }

        // Test on page load
        window.addEventListener('load', function() {
            log('Page loaded. Ready to test PDF generation.');
        });
    </script>
</body>
</html>
