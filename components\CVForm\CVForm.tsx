
import React, { useState, useEffect } from 'react';
import { CVData, PersonalInformation, WorkExperienceItem, EducationTrainingItem, PersonalSkills, Attachment } from '../../types';
import PersonalInformationSection from './sections/PersonalInformationSection';
import WorkExperienceSection from './sections/WorkExperienceSection';
import EducationTrainingSection from './sections/EducationTrainingSection';
import PersonalSkillsSection from './sections/PersonalSkillsSection';
import Input from '../common/Input';
import Textarea from '../common/Textarea';
import Button from '../common/Button';
import Card from '../common/Card';
import RepeatableFieldSection, { ItemActions } from '../common/RepeatableFieldSection';
import { SaveIcon, EyeIcon, PlusCircleIcon, TrashIcon } from 'lucide-react'; // Added PlusCircleIcon, TrashIcon
import { v4 as uuidv4 } from 'uuid';
import { useLanguage } from '../../contexts/LanguageContext';

interface CVFormProps {
  initialData?: CVData;
  onSave: (cvData: CVData) => void;
  onPreview?: (cvData: CVData) => void;
}

const createEmptyAttachment = (): Attachment => ({
  id: uuidv4(),
  name: '',
  description: '',
});

const createEmptyCVData = (): Omit<CVData, 'id' | 'lastUpdate'> => ({
  title: '', // Changed from cvName
  personalInformation: {
    name: '', surname: '', photo: undefined, address: '', postalCode: '', city: '', country: '',
    telephones: [], emailAddresses: [], websites: [], linkedinProfile: '', instantMessaging: [],
    sex: '', dateOfBirth: '', nationality: '',
  },
  desiredEmploymentOrPersonalStatement: '',
  workExperience: [],
  educationTraining: [],
  personalSkills: {
    motherTongue: '', // Singular
    otherLanguages: [],
    communicationSkills: '',
    organisationalOrManagerialSkills: '',
    professionalSkills: '',
    digitalSkills: '',
    artisticSkills: '',
    otherSkills: '',
  },
  drivingLicence: '', // Singular string for categories
  additionalInformation: '',
  attachments: [],
});


const CVForm: React.FC<CVFormProps> = ({ initialData, onSave, onPreview }) => {
  const { t } = useLanguage();
  const [cvData, setCvData] = useState<CVData>(
    initialData || { ...createEmptyCVData(), id: uuidv4(), lastUpdate: new Date().toISOString() }
  );
  const [activeSection, setActiveSection] = useState<string>('personalInformationTitle'); // Use title key

  // Funzione per migrare/validare le esperienze lavorative
  const migrateWorkExperience = (workExperience: WorkExperienceItem[]): WorkExperienceItem[] => {
    return workExperience.map(exp => ({
      ...exp,
      // Assicuriamoci che i nuovi campi esistano con valori di default
      isRecurring: exp.isRecurring ?? false,
      displayAsGroup: exp.displayAsGroup ?? false,
      recurringPattern: exp.recurringPattern ?? undefined,
    }));
  };

  useEffect(() => {
    if (initialData) {
      const validatedInitialData: CVData = {
        ...createEmptyCVData(), // Start with a fully initialized empty structure
        ...initialData,        // Overlay initial data
        id: initialData.id || uuidv4(),
        lastUpdate: initialData.lastUpdate || new Date().toISOString(),
        personalInformation: {
          ...createEmptyCVData().personalInformation,
          ...(initialData.personalInformation || {}),
        },
        personalSkills: {
          ...createEmptyCVData().personalSkills,
          ...(initialData.personalSkills || {}),
        },
        workExperience: migrateWorkExperience(initialData.workExperience || []),
        educationTraining: initialData.educationTraining || [],
        attachments: initialData.attachments || [],
      };
      setCvData(validatedInitialData);
    } else {
      setCvData({ ...createEmptyCVData(), id: uuidv4(), lastUpdate: new Date().toISOString() });
    }
  }, [initialData]);


  const handleInputChange = <K extends keyof CVData>(field: K, value: CVData[K]) => {
    setCvData(prev => ({ ...prev, [field]: value }));
  };

  const handlePersonalInformationChange = <K extends keyof PersonalInformation>(field: K, value: PersonalInformation[K]) => {
    setCvData(prev => ({
      ...prev,
      personalInformation: { ...prev.personalInformation, [field]: value },
    }));
  };
  
  const handlePersonalSkillsChange = <K extends keyof PersonalSkills>(field: K, value: PersonalSkills[K]) => {
    setCvData(prev => ({
      ...prev,
      personalSkills: { ...prev.personalSkills, [field]: value },
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(cvData);
  };
  
  const AttachmentItem: React.FC<{
    item: Attachment;
    onChange: (updatedItem: Attachment) => void;
    onRemove: (id: string) => void;
  }> = ({ item, onChange, onRemove }) => {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 relative pb-2 items-end">
        <ItemActions onRemove={() => onRemove(item.id)} />
        <Input label={t('attachmentsSection.name')} value={item.name} onChange={e => onChange({ ...item, name: e.target.value })} required containerClassName="mb-0" />
        <Textarea label={t('attachmentsSection.description')} value={item.description || ''} onChange={e => onChange({ ...item, description: e.target.value })} rows={1} containerClassName="mb-0" />
      </div>
    );
  };


  const sections = [
    { id: 'personalInformationTitle', titleKey: 'cvForm.personalInformationTitle', component: <PersonalInformationSection data={cvData.personalInformation} onChange={handlePersonalInformationChange} /> },
    { id: 'jobAppliedForTitle', titleKey: 'cvForm.jobAppliedForTitle', component: (
      <Textarea 
        label={t('jobAppliedForSection.label')} 
        value={cvData.desiredEmploymentOrPersonalStatement || ''} 
        onChange={e => handleInputChange('desiredEmploymentOrPersonalStatement', e.target.value)} 
        rows={4}
        placeholder={t('placeholders.enterHere')}
      />
    )},
    { id: 'workExperienceTitle', titleKey: 'cvForm.workExperienceTitle', component: <WorkExperienceSection items={cvData.workExperience} setItems={items => handleInputChange('workExperience', items as WorkExperienceItem[])} /> },
    { id: 'educationTrainingTitle', titleKey: 'cvForm.educationTrainingTitle', component: <EducationTrainingSection items={cvData.educationTraining} setItems={items => handleInputChange('educationTraining', items as EducationTrainingItem[])} /> },
    { id: 'personalSkillsTitle', titleKey: 'cvForm.personalSkillsTitle', component: <PersonalSkillsSection data={cvData.personalSkills} onChange={handlePersonalSkillsChange} /> },
    { id: 'drivingLicenceTitle', titleKey: 'cvForm.drivingLicenceTitle', component: (
      <Input 
        label={t('drivingLicenceSection.label')} 
        value={cvData.drivingLicence || ''} 
        onChange={e => handleInputChange('drivingLicence', e.target.value)}
        placeholder="e.g. B, C1"
      />
    )},
    { id: 'additionalInformationTitle', titleKey: 'cvForm.additionalInformationTitle', component: <Textarea label={t('additionalInfoSection.label')} value={cvData.additionalInformation || ''} onChange={e => handleInputChange('additionalInformation', e.target.value)} rows={5} placeholder={t('placeholders.enterHere')} /> },
    { id: 'attachmentsTitle', titleKey: 'cvForm.attachmentsTitle', component: (
        <RepeatableFieldSection<Attachment>
            items={cvData.attachments || []}
            setItems={items => handleInputChange('attachments', items as Attachment[])}
            renderItem={(item, _index, onChange, onRemove) => (
                <AttachmentItem item={item} onChange={onChange} onRemove={onRemove} />
            )}
            createEmptyItem={createEmptyAttachment}
            title={t('attachmentsSection.title')}
            addItemText={t('attachmentsSection.addItem')}
        />
    )},
  ];

  return (
    <form onSubmit={handleSubmit} className="space-y-8">
      <Card title={t('cvForm.cvTitleLabel')} className="mb-6">
        <Input label={t('cvForm.cvTitleLabel')} value={cvData.title} onChange={e => handleInputChange('title', e.target.value)} placeholder={t('cvForm.cvTitlePlaceholder')} required />
      </Card>

      <div className="flex flex-col md:flex-row gap-6">
        <nav className="md:w-1/4">
          <ul className="sticky top-24 space-y-1">
            {sections.map(section => (
              <li key={section.id}>
                <button
                  type="button"
                  onClick={() => setActiveSection(section.id)}
                  className={`w-full text-left px-3 py-2.5 rounded-md transition-colors text-sm font-medium ${
                    activeSection === section.id ? 'bg-primary-600 text-white' : 'hover:bg-primary-100 text-gray-700'
                  }`}
                  aria-current={activeSection === section.id ? 'page' : undefined}
                >
                  {t(section.titleKey)}
                </button>
              </li>
            ))}
          </ul>
        </nav>

        <div className="md:w-3/4 space-y-6">
          {sections.map(section => (
            <section key={section.id} id={section.id} aria-labelledby={`${section.id}-heading`} className={activeSection === section.id ? 'block' : 'hidden'}>
              <Card title={<span id={`${section.id}-heading`}>{t(section.titleKey)}</span>}>
                {section.component}
              </Card>
            </section>
          ))}
        </div>
      </div>
      
      <div className="mt-8 flex justify-end space-x-3 sticky bottom-0 bg-gray-100/80 backdrop-blur-sm py-4 px-2 border-t border-gray-200">
        {onPreview && (
          <Button type="button" variant="outline" onClick={() => onPreview(cvData)} leftIcon={<EyeIcon size={18}/>}>
            {t('cvForm.buttons.previewPDF')}
          </Button>
        )}
        <Button type="submit" variant="primary" leftIcon={<SaveIcon size={18}/>}>
          {t('cvForm.buttons.saveCV')}
        </Button>
      </div>
    </form>
  );
};

export default CVForm;
