
import React, { ChangeEvent, useState } from 'react';
import { PersonalInformation, InstantMessaging } from '../../../types';
import Input from '../../common/Input';
import Button from '../../common/Button';
import Select from '../../common/Select';
import { PlusIcon, TrashIcon, UploadCloudIcon, XIcon } from 'lucide-react';
import { useLanguage } from '../../../contexts/LanguageContext';
import { v4 as uuidv4 } from 'uuid';

interface PersonalInformationSectionProps {
  data: PersonalInformation;
  onChange: <K extends keyof PersonalInformation>(field: K, value: PersonalInformation[K]) => void;
}

const createEmptyInstantMessaging = (): InstantMessaging => ({
  id: uuidv4(),
  service: '',
  username: '',
});

const PersonalInformationSection: React.FC<PersonalInformationSectionProps> = ({ data, onChange }) => {
  const { t } = useLanguage();
  const [photoPreview, setPhotoPreview] = useState<string | null>(data.photo || null);

  const handleFieldChange = <K extends keyof PersonalInformation>(field: K, value: PersonalInformation[K]) => {
    onChange(field, value);
  };

  const handleListChange = (field: 'telephones' | 'emailAddresses' | 'websites', index: number, value: string) => {
    const newList = [...(data[field] || [])];
    newList[index] = value;
    handleFieldChange(field, newList as any);
  };

  const addToList = (field: 'telephones' | 'emailAddresses' | 'websites') => {
    const newList = [...(data[field] || []), ''];
    handleFieldChange(field, newList as any);
  };

  const removeFromList = (field: 'telephones' | 'emailAddresses' | 'websites', index: number) => {
    const newList = (data[field] || []).filter((_, i) => i !== index);
    handleFieldChange(field, newList as any);
  };

  const handleIMChange = (index: number, field: keyof Omit<InstantMessaging, 'id'>, value: string) => {
    const newIMs = [...(data.instantMessaging || [])];
    newIMs[index] = { ...newIMs[index], [field]: value };
    onChange('instantMessaging', newIMs);
  };

  const addIM = () => {
    const newIMs = [...(data.instantMessaging || []), createEmptyInstantMessaging()];
    onChange('instantMessaging', newIMs);
  };

  const removeIM = (id: string) => {
    const newIMs = (data.instantMessaging || []).filter(im => im.id !== id);
    onChange('instantMessaging', newIMs);
  };
  
  const handlePhotoUpload = (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64String = reader.result as string;
        onChange('photo', base64String);
        setPhotoPreview(base64String);
      };
      reader.readAsDataURL(file);
    }
  };

  const removePhoto = () => {
    onChange('photo', undefined);
    setPhotoPreview(null);
  };

  const sexOptions = [
    { value: '', label: t('personalInfoSection.sexOptions.select') },
    { value: 'Male', label: t('personalInfoSection.sexOptions.male') },
    { value: 'Female', label: t('personalInfoSection.sexOptions.female') },
    { value: 'Unspecified', label: t('personalInfoSection.sexOptions.unspecified') },
  ];

  const listFieldMeta = {
    telephones: { label: t('personalInfoSection.telephone'), placeholder: t('personalInfoSection.placeholderTelephone'), addText: t('personalInfoSection.addTelephone'), type: 'tel' },
    emailAddresses: { label: t('personalInfoSection.email'), placeholder: t('personalInfoSection.placeholderEmail'), addText: t('personalInfoSection.addEmail'), type: 'email' },
    websites: { label: t('personalInfoSection.website'), placeholder: t('personalInfoSection.placeholderWebsite'), addText: t('personalInfoSection.addWebsite'), type: 'url' },
  };


  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Input label={t('personalInfoSection.name')} value={data.name || ''} onChange={e => handleFieldChange('name', e.target.value)} required />
        <Input label={t('personalInfoSection.surname')} value={data.surname || ''} onChange={e => handleFieldChange('surname', e.target.value)} required />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">{t('personalInfoSection.photo')}</label>
        <div className="mt-1 flex items-center space-x-4">
          {photoPreview ? (
            <div className="relative">
              <img src={photoPreview} alt="CV Photo Preview" className="w-24 h-24 rounded-full object-cover border border-gray-300" />
              <Button type="button" onClick={removePhoto} variant="ghost" size="sm" className="absolute -top-2 -right-2 bg-white rounded-full p-1 text-red-500 hover:text-red-700 shadow">
                <XIcon size={16} />
              </Button>
            </div>
          ) : (
             <div className="w-24 h-24 rounded-full bg-gray-100 flex items-center justify-center text-gray-400 border border-gray-300">
                <UploadCloudIcon size={32} />
             </div>
          )}
          <div>
            <input type="file" id="photo-upload" accept="image/png, image/jpeg, image/gif" onChange={handlePhotoUpload} className="hidden" />
            <label htmlFor="photo-upload" className="cursor-pointer bg-white py-2 px-3 border border-gray-300 rounded-md shadow-sm text-sm leading-4 font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
              {photoPreview ? t('personalInfoSection.photoChange') : t('personalInfoSection.photoUpload')}
            </label>
          </div>
        </div>
         <p className="text-xs text-gray-500 mt-1">{t('pdf.photo')} {t('placeholders.select')} (max 2MB, .jpg, .png)</p>
      </div>
      
      <Input label={t('personalInfoSection.address')} value={data.address || ''} onChange={e => handleFieldChange('address', e.target.value)} placeholder="e.g. 123 Main Street, Anytown, USA 12345" />
      {/* Postal Code, City, Country can be part of address or separate as per previous structure. For Europass, one line address is common. */}
      {/* Keeping them separate for now if user prefers granular input */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Input label={t('personalInfoSection.postalCode')} value={data.postalCode || ''} onChange={e => handleFieldChange('postalCode', e.target.value)} />
        <Input label={t('personalInfoSection.city')} value={data.city || ''} onChange={e => handleFieldChange('city', e.target.value)} />
        <Input label={t('personalInfoSection.country')} value={data.country || ''} onChange={e => handleFieldChange('country', e.target.value)} />
      </div>


      {(['telephones', 'emailAddresses', 'websites'] as const).map(fieldKey => (
        <div key={fieldKey}>
          <label className="block text-sm font-medium text-gray-700 mb-2">{listFieldMeta[fieldKey].label}</label>
          {(data[fieldKey] || []).map((item, index) => (
            <div key={index} className="flex items-center mb-2">
              <Input
                containerClassName="flex-grow mb-0 mr-2"
                type={listFieldMeta[fieldKey].type as string}
                value={item}
                onChange={e => handleListChange(fieldKey, index, e.target.value)}
                placeholder={listFieldMeta[fieldKey].placeholder}
              />
              <Button type="button" variant="ghost" size="sm" onClick={() => removeFromList(fieldKey, index)} className="text-red-500 p-1" aria-label={`Remove ${fieldKey.slice(0, -1)}`}>
                <TrashIcon size={16} />
              </Button>
            </div>
          ))}
          <Button type="button" variant="outline" size="sm" onClick={() => addToList(fieldKey)} leftIcon={<PlusIcon size={16} />}>
            {listFieldMeta[fieldKey].addText}
          </Button>
        </div>
      ))}

      <Input label={t('personalInfoSection.linkedinProfile')} value={data.linkedinProfile || ''} onChange={e => handleFieldChange('linkedinProfile', e.target.value)} placeholder="e.g. https://linkedin.com/in/yourprofile" />
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">{t('personalInfoSection.instantMessaging')}</label>
        {(data.instantMessaging || []).map((im, index) => (
          <div key={im.id} className="grid grid-cols-1 sm:grid-cols-3 gap-2 mb-2 p-2 border border-gray-200 rounded-md items-end bg-gray-50 relative">
            <Input label={t('personalInfoSection.imService')} value={im.service} onChange={e => handleIMChange(index, 'service', e.target.value)} containerClassName="mb-0" />
            <Input label={t('personalInfoSection.imUsername')} value={im.username} onChange={e => handleIMChange(index, 'username', e.target.value)} containerClassName="mb-0 sm:col-span-2" />
             <div className="absolute top-1 right-1">
                <Button type="button" variant="ghost" size="sm" onClick={() => removeIM(im.id)} className="text-red-500 p-1" aria-label="Remove instant messaging entry">
                    <TrashIcon size={16} />
                </Button>
            </div>
          </div>
        ))}
        <Button type="button" variant="outline" size="sm" onClick={addIM} leftIcon={<PlusIcon size={16} />}>
          {t('personalInfoSection.addIM')}
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Select
          label={t('personalInfoSection.sex')}
          value={data.sex || ''}
          onChange={e => handleFieldChange('sex', e.target.value)}
          options={sexOptions}
          placeholder={t('placeholders.select')}
        />
        <Input label={t('personalInfoSection.dateOfBirth')} type="date" value={data.dateOfBirth || ''} onChange={e => handleFieldChange('dateOfBirth', e.target.value)} />
        <Input label={t('personalInfoSection.nationality')} value={data.nationality || ''} onChange={e => handleFieldChange('nationality', e.target.value)} />
      </div>
    </div>
  );
};

export default PersonalInformationSection;
