import React, { useState, useEffect } from 'react';
import { WorkExperienceItem, RecurringPattern } from '../../../types';
import { v4 as uuidv4 } from 'uuid';
import Input from '../../common/Input';
import Textarea from '../../common/Textarea';
import Button from '../../common/Button';
import Select from '../../common/Select';
import { useLanguage } from '../../../contexts/LanguageContext';
import { X } from 'lucide-react';

interface RecurringWorkExperienceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (workExperience: WorkExperienceItem) => void;
  initialData?: Partial<WorkExperienceItem>;
}

const RecurringWorkExperienceModal: React.FC<RecurringWorkExperienceModalProps> = ({
  isOpen,
  onClose,
  onSave,
  initialData
}) => {
  const { t } = useLanguage();
  
  const [formData, setFormData] = useState<WorkExperienceItem>(() => {
    if (initialData && initialData.isRecurring) {
      // Se stiamo modificando un'esperienza ricorrente esistente
      return {
        ...initialData,
        isRecurring: true,
        displayAsGroup: initialData.displayAsGroup ?? true,
        recurringPattern: initialData.recurringPattern || {
          startYear: new Date().getFullYear() - 10,
          endYear: new Date().getFullYear() - 1,
          startMonth: 6,
          endMonth: 9,
          description: ''
        }
      };
    } else {
      // Se stiamo creando una nuova esperienza ricorrente
      return {
        id: uuidv4(),
        startDate: '',
        endDate: '',
        current: false,
        occupationOrPositionHeld: '',
        employer: '',
        employerAddress: '',
        typeOfBusinessOrSector: '',
        mainActivitiesAndResponsibilities: '',
        isRecurring: true,
        displayAsGroup: true,
        recurringPattern: {
          startYear: new Date().getFullYear() - 10,
          endYear: new Date().getFullYear() - 1,
          startMonth: 6, // Giugno
          endMonth: 9, // Settembre
          description: ''
        },
        ...initialData
      };
    }
  });

  // Aggiorna i dati del form quando initialData cambia
  useEffect(() => {
    if (isOpen) {
      if (initialData && initialData.isRecurring) {
        // Se stiamo modificando un'esperienza ricorrente esistente
        setFormData({
          ...initialData,
          isRecurring: true,
          displayAsGroup: initialData.displayAsGroup ?? true,
          recurringPattern: initialData.recurringPattern || {
            startYear: new Date().getFullYear() - 10,
            endYear: new Date().getFullYear() - 1,
            startMonth: 6,
            endMonth: 9,
            description: ''
          }
        });
      } else {
        // Se stiamo creando una nuova esperienza ricorrente
        setFormData({
          id: uuidv4(),
          startDate: '',
          endDate: '',
          current: false,
          occupationOrPositionHeld: '',
          employer: '',
          employerAddress: '',
          typeOfBusinessOrSector: '',
          mainActivitiesAndResponsibilities: '',
          isRecurring: true,
          displayAsGroup: true,
          recurringPattern: {
            startYear: new Date().getFullYear() - 10,
            endYear: new Date().getFullYear() - 1,
            startMonth: 6,
            endMonth: 9,
            description: ''
          }
        });
      }
    }
  }, [isOpen, initialData]);

  const monthOptions = [
    { value: 1, label: t('months.january') },
    { value: 2, label: t('months.february') },
    { value: 3, label: t('months.march') },
    { value: 4, label: t('months.april') },
    { value: 5, label: t('months.may') },
    { value: 6, label: t('months.june') },
    { value: 7, label: t('months.july') },
    { value: 8, label: t('months.august') },
    { value: 9, label: t('months.september') },
    { value: 10, label: t('months.october') },
    { value: 11, label: t('months.november') },
    { value: 12, label: t('months.december') }
  ];

  const handleChange = (field: keyof WorkExperienceItem, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handlePatternChange = (field: keyof RecurringPattern, value: any) => {
    setFormData(prev => ({
      ...prev,
      recurringPattern: {
        ...prev.recurringPattern!,
        [field]: value
      }
    }));
  };

  const handleSave = () => {
    // Genera le date di inizio e fine basate sul pattern nel formato YYYY-MM (per input type="month")
    const pattern = formData.recurringPattern!;
    const startDate = `${pattern.startYear}-${pattern.startMonth.toString().padStart(2, '0')}`;
    const endDate = `${pattern.endYear}-${pattern.endMonth.toString().padStart(2, '0')}`;

    const workExperience: WorkExperienceItem = {
      ...formData,
      startDate,
      endDate
    };

    onSave(workExperience);
    onClose();
  };

  const generateYearOptions = () => {
    const currentYear = new Date().getFullYear();
    const years = [];
    for (let year = currentYear; year >= currentYear - 50; year--) {
      years.push({ value: year, label: year.toString() });
    }
    return years;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold text-gray-800">
            {initialData && initialData.isRecurring
              ? t('recurringWorkExperience.editTitle')
              : t('recurringWorkExperience.title')
            }
          </h2>
          <Button
            type="button"
            onClick={onClose}
            variant="ghost"
            size="sm"
            className="text-gray-500 hover:text-gray-700"
          >
            <X size={20} />
          </Button>
        </div>

        <div className="space-y-4">
          {/* Informazioni base del lavoro */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label={t('workExperienceSection.occupationOrPositionHeld')}
              value={formData.occupationOrPositionHeld}
              onChange={e => handleChange('occupationOrPositionHeld', e.target.value)}
              required
              containerClassName="md:col-span-2"
            />
            <Input
              label={t('workExperienceSection.employer')}
              value={formData.employer}
              onChange={e => handleChange('employer', e.target.value)}
              required
            />
            <Input
              label={t('workExperienceSection.employerAddress')}
              value={formData.employerAddress || ''}
              onChange={e => handleChange('employerAddress', e.target.value)}
            />
            <Input
              label={t('workExperienceSection.typeOfBusinessOrSector')}
              value={formData.typeOfBusinessOrSector || ''}
              onChange={e => handleChange('typeOfBusinessOrSector', e.target.value)}
              containerClassName="md:col-span-2"
            />
          </div>

          {/* Pattern ricorrente */}
          <div className="border-t pt-4">
            <h3 className="text-lg font-medium text-gray-700 mb-3">
              {t('recurringWorkExperience.patternTitle')}
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Select
                label={t('recurringWorkExperience.startYear')}
                value={formData.recurringPattern?.startYear || ''}
                onChange={e => handlePatternChange('startYear', parseInt(e.target.value))}
                options={generateYearOptions()}
                required
              />
              <Select
                label={t('recurringWorkExperience.endYear')}
                value={formData.recurringPattern?.endYear || ''}
                onChange={e => handlePatternChange('endYear', parseInt(e.target.value))}
                options={generateYearOptions()}
                required
              />
              <Select
                label={t('recurringWorkExperience.startMonth')}
                value={formData.recurringPattern?.startMonth || ''}
                onChange={e => handlePatternChange('startMonth', parseInt(e.target.value))}
                options={monthOptions}
                required
              />
              <Select
                label={t('recurringWorkExperience.endMonth')}
                value={formData.recurringPattern?.endMonth || ''}
                onChange={e => handlePatternChange('endMonth', parseInt(e.target.value))}
                options={monthOptions}
                required
              />
            </div>
            
            <div className="mt-3">
              <Input
                label={t('recurringWorkExperience.patternDescription')}
                value={formData.recurringPattern?.description || ''}
                onChange={e => handlePatternChange('description', e.target.value)}
                placeholder={t('recurringWorkExperience.patternDescriptionPlaceholder')}
              />
            </div>
          </div>

          {/* Attività e responsabilità */}
          <div>
            <Textarea
              label={t('workExperienceSection.mainActivitiesAndResponsibilities')}
              value={formData.mainActivitiesAndResponsibilities}
              onChange={e => handleChange('mainActivitiesAndResponsibilities', e.target.value)}
              rows={4}
              placeholder={t('recurringWorkExperience.activitiesPlaceholder')}
            />
          </div>

          {/* Opzioni di visualizzazione */}
          <div className="border-t pt-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.displayAsGroup || false}
                onChange={e => handleChange('displayAsGroup', e.target.checked)}
                className="mr-2 h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
              />
              {t('recurringWorkExperience.displayAsGroup')}
            </label>
            <p className="text-sm text-gray-600 mt-1">
              {t('recurringWorkExperience.displayAsGroupHelp')}
            </p>
          </div>
        </div>

        {/* Pulsanti di azione */}
        <div className="flex justify-end space-x-3 mt-6 pt-4 border-t">
          <Button
            type="button"
            onClick={onClose}
            variant="outline"
          >
            {t('common.cancel')}
          </Button>
          <Button
            type="button"
            onClick={handleSave}
            variant="primary"
          >
            {t('common.save')}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default RecurringWorkExperienceModal;
