import React from 'react';
import { WorkExperienceItem, RecurringPattern } from '../../../types';
import { useLanguage } from '../../../contexts/LanguageContext';
import { expandRecurringExperience, formatRecurringPattern } from '../../../utils/recurringExperienceUtils';
import { Calendar, Eye, List } from 'lucide-react';

interface RecurringExperiencePreviewProps {
  workExperience: WorkExperienceItem;
  showExpanded?: boolean;
}

const RecurringExperiencePreview: React.FC<RecurringExperiencePreviewProps> = ({
  workExperience,
  showExpanded = false
}) => {
  const { t } = useLanguage();

  if (!workExperience.isRecurring || !workExperience.recurringPattern) {
    return null;
  }

  const expandedExperiences = expandRecurringExperience(workExperience);
  const pattern = workExperience.recurringPattern;

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-4">
      <div className="flex items-center mb-3">
        <Calendar size={20} className="text-blue-600 mr-2" />
        <h4 className="text-lg font-medium text-blue-800">
          {t('recurringWorkExperience.recurringExperience')}
        </h4>
      </div>

      {/* Pattern Summary */}
      <div className="mb-3">
        <div className="flex items-center text-sm text-blue-700 mb-2">
          <Eye size={16} className="mr-2" />
          <span className="font-medium">{t('recurringWorkExperience.patternTitle')}:</span>
        </div>
        <p className="text-blue-800 font-medium">
          {formatRecurringPattern(pattern, t)}
        </p>
        {pattern.description && (
          <p className="text-blue-600 text-sm mt-1 italic">
            {pattern.description}
          </p>
        )}
      </div>

      {/* Display Mode Info */}
      <div className="mb-3">
        <div className="flex items-center text-sm">
          <span className="text-blue-700 mr-2">
            {t('pdf.displayMode')}:
          </span>
          <span className={`px-2 py-1 rounded text-xs font-medium ${
            workExperience.displayAsGroup 
              ? 'bg-green-100 text-green-800' 
              : 'bg-yellow-100 text-yellow-800'
          }`}>
            {workExperience.displayAsGroup 
              ? t('recurringWorkExperience.compactDisplay')
              : t('recurringWorkExperience.expandedDisplay')
            }
          </span>
        </div>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4 text-sm">
        <div className="text-center">
          <div className="text-blue-800 font-semibold">
            {pattern.endYear - pattern.startYear + 1}
          </div>
          <div className="text-blue-600">
            {t('recurringWorkExperience.totalYears')}
          </div>
        </div>
        <div className="text-center">
          <div className="text-blue-800 font-semibold">
            {pattern.endMonth - pattern.startMonth + 1}
          </div>
          <div className="text-blue-600">
            {t('recurringWorkExperience.monthsPerYear')}
          </div>
        </div>
        <div className="text-center">
          <div className="text-blue-800 font-semibold">
            {expandedExperiences.length}
          </div>
          <div className="text-blue-600">
            {t('recurringWorkExperience.totalPeriods')}
          </div>
        </div>
        <div className="text-center">
          <div className="text-blue-800 font-semibold">
            {(pattern.endYear - pattern.startYear + 1) * (pattern.endMonth - pattern.startMonth + 1)}
          </div>
          <div className="text-blue-600">
            {t('recurringWorkExperience.totalMonths')}
          </div>
        </div>
      </div>

      {/* Expanded View Toggle */}
      {showExpanded && (
        <div className="border-t border-blue-200 pt-4">
          <div className="flex items-center mb-3">
            <List size={16} className="text-blue-600 mr-2" />
            <span className="text-sm font-medium text-blue-700">
              {t('recurringWorkExperience.expandedView')}:
            </span>
          </div>
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {expandedExperiences.map((exp, index) => (
              <div key={index} className="bg-white rounded p-2 text-sm border border-blue-100">
                <div className="font-medium text-gray-800">
                  {exp.startDate} - {exp.endDate}
                </div>
                <div className="text-gray-600">
                  {exp.occupationOrPositionHeld} @ {exp.employer}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* PDF Preview Note */}
      <div className="mt-4 p-3 bg-blue-100 rounded border border-blue-200">
        <p className="text-xs text-blue-700">
          <strong>{t('recurringWorkExperience.pdfNote')}:</strong>{' '}
          {workExperience.displayAsGroup 
            ? t('recurringWorkExperience.pdfNoteCompact')
            : t('recurringWorkExperience.pdfNoteExpanded')
          }
        </p>
      </div>
    </div>
  );
};

export default RecurringExperiencePreview;
