// API service for CV data
import { CVData } from '../types';

const API_URL = process.env.API_URL || 'http://localhost:3000/api';

export const fetchCVs = async (): Promise<CVData[]> => {
  const response = await fetch(`${API_URL}/cvs`);
  if (!response.ok) throw new Error('Failed to fetch CVs');
  return response.json();
};

export const fetchCV = async (id: string): Promise<CVData> => {
  const response = await fetch(`${API_URL}/cvs/${id}`);
  if (!response.ok) throw new Error('CV not found');
  return response.json();
};

export const createCV = async (cvData: Omit<CVData, 'id' | 'lastUpdate'>): Promise<CVData> => {
  const response = await fetch(`${API_URL}/cvs`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(cvData),
  });
  if (!response.ok) throw new Error('Failed to create CV');
  return response.json();
};

export const updateCV = async (cvData: CVData): Promise<CVData> => {
  const response = await fetch(`${API_URL}/cvs/${cvData.id}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(cvData),
  });
  if (!response.ok) throw new Error('Failed to update CV');
  return response.json();
};

export const deleteCV = async (id: string): Promise<void> => {
  const response = await fetch(`${API_URL}/cvs/${id}`, { method: 'DELETE' });
  if (!response.ok) throw new Error('Failed to delete CV');
};