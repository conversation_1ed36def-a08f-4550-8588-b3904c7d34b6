<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Test</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.8.2/jspdf-autotable.min.js"></script>
</head>
<body>
    <h1>PDF Test</h1>
    <button onclick="testPDF()">Test PDF Generation</button>
    
    <script>
        function testPDF() {
            console.log('Testing PDF generation...');
            
            // Verifica che jsPDF sia disponibile
            if (typeof window.jsPDF === 'undefined') {
                console.error('jsPDF is not loaded');
                alert('jsPDF is not loaded');
                return;
            }
            
            console.log('jsPDF is available');
            
            // Crea un'istanza di jsPDF
            const doc = new window.jsPDF();
            
            // Verifica che autoTable sia disponibile
            if (typeof doc.autoTable !== 'function') {
                console.error('autoTable is not available');
                console.log('Available methods:', Object.getOwnPropertyNames(doc));
                alert('autoTable is not available');
                return;
            }
            
            console.log('autoTable is available');
            
            // Test di base
            doc.text('Test PDF', 20, 20);
            
            // Test autoTable
            doc.autoTable({
                head: [['Name', 'Email', 'Country']],
                body: [
                    ['David', '<EMAIL>', 'Sweden'],
                    ['Castille', '<EMAIL>', 'Spain'],
                ],
                startY: 30,
            });
            
            doc.save('test.pdf');
            console.log('PDF generated successfully!');
            alert('PDF generated successfully!');
        }
    </script>
</body>
</html>
