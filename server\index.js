import express from 'express';
import cors from 'cors';
import { v4 as uuidv4 } from 'uuid';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3000;
const DATA_FILE = path.join(__dirname, 'data', 'cvs.json');

// Middleware
app.use(cors());
app.use(express.json({ limit: '50mb' })); // Increased limit for image uploads

// Ensure data directory exists
const ensureDataDir = async () => {
  try {
    await fs.mkdir(path.join(__dirname, 'data'), { recursive: true });
  } catch (err) {
    console.error('Error creating data directory:', err);
  }
};

// Load CVs from file
const loadCVs = async () => {
  try {
    await ensureDataDir();
    const data = await fs.readFile(DATA_FILE, 'utf8');
    return JSON.parse(data);
  } catch (err) {
    // If file doesn't exist, return empty array
    return [];
  }
};

// Save CVs to file
const saveCVs = async (cvs) => {
  await ensureDataDir();
  await fs.writeFile(DATA_FILE, JSON.stringify(cvs, null, 2));
};

// Routes
app.get('/api/cvs', async (req, res) => {
  try {
    const cvs = await loadCVs();
    res.json(cvs);
  } catch (err) {
    res.status(500).json({ error: 'Failed to load CVs' });
  }
});

app.get('/api/cvs/:id', async (req, res) => {
  try {
    const cvs = await loadCVs();
    const cv = cvs.find(cv => cv.id === req.params.id);
    if (!cv) {
      return res.status(404).json({ error: 'CV not found' });
    }
    res.json(cv);
  } catch (err) {
    res.status(500).json({ error: 'Failed to load CV' });
  }
});

app.post('/api/cvs', async (req, res) => {
  try {
    const cvs = await loadCVs();
    const newCV = {
      ...req.body,
      id: uuidv4(),
      lastUpdate: new Date().toISOString()
    };
    cvs.push(newCV);
    await saveCVs(cvs);
    res.status(201).json(newCV);
  } catch (err) {
    res.status(500).json({ error: 'Failed to create CV' });
  }
});

app.put('/api/cvs/:id', async (req, res) => {
  try {
    const cvs = await loadCVs();
    const index = cvs.findIndex(cv => cv.id === req.params.id);
    if (index === -1) {
      return res.status(404).json({ error: 'CV not found' });
    }

    const updatedCV = {
      ...req.body,
      lastUpdate: new Date().toISOString()
    };
    cvs[index] = updatedCV;
    await saveCVs(cvs);
    res.json(updatedCV);
  } catch (err) {
    res.status(500).json({ error: 'Failed to update CV' });
  }
});

app.delete('/api/cvs/:id', async (req, res) => {
  try {
    const cvs = await loadCVs();
    const filteredCVs = cvs.filter(cv => cv.id !== req.params.id);
    if (filteredCVs.length === cvs.length) {
      return res.status(404).json({ error: 'CV not found' });
    }
    await saveCVs(filteredCVs);
    res.status(204).send();
  } catch (err) {
    res.status(500).json({ error: 'Failed to delete CV' });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});